# 灰度传感器循迹实现文档

## 📋 **实现概述**
- **实现时间**: 2025-01-31
- **实现方式**: 基于暴力解法的6路灰度传感器循迹
- **传感器配置**: OUT1,OUT3,OUT4,OUT5,OUT6,OUT8 (共6路)
- **控制策略**: 数字量高低电平检测 (白色=0, 黑色=1)

## 🔌 **硬件连接确认**

### **灰度传感器引脚映射 (7传感器配置)**
| 传感器输出 | MCU引脚 | 数组索引 | 功能描述 |
|------------|---------|----------|----------|
| **OUT1** | **PA9** | **[0]** | 最右侧传感器 |
| **OUT2** | **PB7** | **[1]** | 右侧传感器 (新添加) |
| **OUT3** | **PA27** | **[2]** | 右中传感器 |
| **OUT4** | **PA24** | **[3]** | 中央传感器 |
| **OUT5** | **PB16** | **[4]** | 左中传感器 |
| **OUT6** | **PA12** | **[5]** | 左侧传感器 |
| **OUT8** | **PB6** | **[6]** | 最左侧传感器 |

### **传感器排列示意 (7传感器)**
```
[0] [1] [2] [3] [4] [5] [6]
OUT1 OUT2 OUT3 OUT4 OUT5 OUT6 OUT8
PA9  PB7  PA27 PA24 PB16 PA12 PB6
右←              中央            →左
```

## 💻 **代码实现**

### **新增文件**
1. **`Control/line_follow.c`** - 循迹控制主实现
2. **`Control/line_follow.h`** - 循迹控制头文件

### **修改文件**
1. **`empty.c`** - 添加循迹模块调用
2. **`Control/balance.c`** - 开放set_pwm函数供外部调用
3. **`Control/balance.h`** - 添加set_pwm函数声明

## 🎯 **智能循迹算法 (全面重新设计)**

### **关键修复和改进**
1. **GPIO读取逻辑修复**: 灰度传感器检测到黑线时输出低电平，需要反转逻辑
2. **HDPIN_4配置修复**: 添加缺失的PULL_DOWN配置
3. **算法彻底重构**: 从简单if-else改为基于位置计算的智能算法
4. **多场景支持**: 支持直线、转弯、丢线、噪声等各种复杂场景
5. **状态机管理**: 添加状态机管理不同的循迹状态
6. **详细调试**: 添加完整的调试信息输出

### **核心算法原理**
- **位置计算**: 基于传感器权重的加权平均位置计算
- **多级响应**: 根据位置偏差程度采用不同强度的转向
- **状态机**: NORMAL/LOST/TURNING/RECOVERING四种状态
- **逆时针偏好**: 所有模糊情况优先选择左转

### **速度参数定义 (参考CSDN文章优化，最高速度2000)**
```c
#define NORMAL_SPEED    1500    // 正常直行速度 (降低基础速度确保稳定)
#define HIGH_SPEED      2000    // 高速(转向时快轮) - 最高不超过2000
#define LOW_SPEED       800     // 低速(转向时慢轮)
#define TURN_SPEED      1200    // 转弯时的基础速度
#define STOP_SPEED      0       // 停止
```

### **简化直线循迹策略 (专注基础功能)**

**传感器排列**: [0][1][2][3][4][5] 对应 OUT1,OUT3,OUT4,OUT5,OUT6,OUT8
**从右到左**: R3, R2, R1, L1, L2, L3
**中央传感器**: [2][3] 对应 OUT4,OUT5

#### **情况1: 理想状态 - 中央传感器检测到黑线**
```c
if (sensor_values[2] == 1 && sensor_values[3] == 1) ||  // 001100
   (sensor_values[2] == 1 && sensor_values[3] == 0) ||  // 001000
   (sensor_values[2] == 0 && sensor_values[3] == 1)     // 000100
```
**动作**: 直行 (NORMAL_SPEED, NORMAL_SPEED)
**说明**: 小车在线中央，保持直行

#### **情况2: 偏右 - 右侧传感器检测到黑线**
```c
else if (sensor_values[0] == 1 || sensor_values[1] == 1)
```
- **严重偏右** (sensor_values[0] == 1): 大幅左转 (HIGH_SPEED, LOW_SPEED)
- **中度偏右** (sensor_values[1] == 1): 中等左转 (HIGH_SPEED, NORMAL_SPEED)

#### **情况3: 偏左 - 左侧传感器检测到黑线**
```c
else if (sensor_values[4] == 1 || sensor_values[5] == 1)
```
- **严重偏左** (sensor_values[5] == 1): 大幅右转 (LOW_SPEED, HIGH_SPEED)
- **中度偏左** (sensor_values[4] == 1): 中等右转 (NORMAL_SPEED, HIGH_SPEED)

#### **情况4: 多传感器检测到线 (转弯处)**
```c
else if (sensor_count >= 3)
```
**动作**: 逆时针左转 (HIGH_SPEED, TURN_SPEED)
**说明**: 检测到转弯或宽线

#### **情况5: 丢线处理**
```c
if (sensor_count == 0)
```
- **短时丢线** (< 20次): 保持前进 (TURN_SPEED, TURN_SPEED)
- **长时丢线** (≥ 20次): 逆时针搜索 (HIGH_SPEED, STOP_SPEED)

### **状态机管理**
- **LINE_FOLLOW_NORMAL**: 正常循迹状态
- **LINE_FOLLOW_LOST**: 丢线状态
- **LINE_FOLLOW_TURNING**: 转弯状态
- **LINE_FOLLOW_RECOVERING**: 恢复状态

### **调试信息**
```c
debug_info {
    sensors[6];      // 传感器状态
    position;        // 计算位置
    count;          // 传感器数量
    state;          // 当前状态
    lost_counter;   // 丢线计数
    left_speed;     // 左电机速度
    right_speed;    // 右电机速度
}
```

#### **情况4: 中间传感器检测到黑线**
```c
else if (sensor_values[2] == 1 && sensor_values[3] == 1)
```
**动作**: 直行 (NORMAL_SPEED, NORMAL_SPEED)

#### **情况5: 只有最左侧传感器检测到黑线**
```c
else if (sensor_values[0] == 1 && sensor_values[1] == 0)
```
**动作**: 急转左 (HIGH_SPEED, STOP_SPEED)

#### **情况6: 只有最右侧传感器检测到黑线**
```c
else if (sensor_values[5] == 1 && sensor_values[4] == 0)
```
**动作**: 急转右 (STOP_SPEED, HIGH_SPEED)

#### **情况7: 所有传感器都检测不到黑线**
```c
else if (sensor_values[0] == 0 && sensor_values[1] == 0 && sensor_values[2] == 0 && 
         sensor_values[3] == 0 && sensor_values[4] == 0 && sensor_values[5] == 0)
```
**动作**: 停止 (STOP_SPEED, STOP_SPEED)

#### **情况8: 其他情况**
**动作**: 默认直行 (NORMAL_SPEED, NORMAL_SPEED)

## 🔧 **函数接口**

### **主要函数**
```c
void line_follow_control(void);     // 循迹控制主函数
void line_follow_init(void);        // 初始化函数
int get_sensor_value(int index);    // 获取传感器状态(调试用)
```

### **调用方式**
```c
// 在主循环中每5ms调用一次
line_follow_control();
```

## ⚙️ **系统集成**

### **主程序修改**
```c
// empty.c 中的修改
#include "line_follow.h"

// 在5ms中断中调用
// BalanceControlTask();  // 暂时注释掉原有控制
line_follow_control();    // 启用灰度传感器循迹控制
```

### **PWM控制函数修改**
```c
// balance.c 中的修改
void set_pwm(int left,int right)  // 移除static，供外部调用
{
    // 限制PWM值范围
    if (left > 8000) left = 8000;
    if (left < 0) left = 0;
    if (right > 8000) right = 8000;
    if (right < 0) right = 0;
    
    // 设置电机方向为正转
    // 设置PWM值 (已修正硬件对应关系)
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, right, GPIO_PWM_0_C0_IDX);  // 右电机
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, left, GPIO_PWM_0_C1_IDX);   // 左电机
}
```

## 🧪 **测试验证**

### **测试步骤**
1. **编译代码**: 确保无编译错误
2. **下载程序**: 烧录到MCU
3. **硬件检查**: 确认灰度传感器接线正确
4. **功能测试**: 
   - 将小车放在黑线上
   - 观察循迹效果
   - 检查转向响应

### **调试接口**
```c
// 获取传感器状态用于调试
int sensor_0 = get_sensor_value(0);  // 最左侧传感器
int sensor_5 = get_sensor_value(5);  // 最右侧传感器
```

## 📈 **后续优化方向**

### **短期优化**
1. **速度参数调优**: 根据实际测试调整NORMAL_SPEED等参数
2. **转向灵敏度**: 调整HIGH_SPEED和LOW_SPEED的差值
3. **丢线处理**: 优化所有传感器都检测不到黑线时的处理策略

### **长期优化**
1. **PID控制**: 将暴力解法升级为PID控制算法
2. **加权平均**: 使用传感器位置加权计算偏差值
3. **自适应速度**: 根据弯道情况自动调整速度
4. **前瞻控制**: 增加前瞻距离提高循迹稳定性

## ✅ **实现状态**

- ✅ **硬件配置完成**: 6路灰度传感器GPIO配置正确
- ✅ **代码实现完成**: 循迹控制逻辑已实现
- ✅ **系统集成完成**: 已集成到主控制循环
- ✅ **电机驱动完成**: 完整的电机驱动程序已集成
- ✅ **PWM启动完成**: PWM定时器自动启动
- ✅ **调试功能完成**: LED指示循迹工作状态
- ✅ **100x100cm优化**: 针对正方形黑线循迹优化参数
- 🔄 **待测试验证**: 需要实际硬件测试验证效果

**实现工程师**: Alex (Engineer)  
**文档完成时间**: 2025-01-31

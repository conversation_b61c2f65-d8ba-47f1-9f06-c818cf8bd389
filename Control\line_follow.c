/**
 * @file line_follow.c
 * @brief 灰度传感器循迹控制模块
 * <AUTHOR> (Engineer)
 * @date 2025-01-31
 */

#include "ti_msp_dl_config.h"
#include "balance.h"

// 灰度传感器引脚定义 (对应OUT1,OUT2,OUT3,OUT4,OUT5,OUT6,OUT8)
#define SENSOR_COUNT 7

// 传感器引脚映射表 (按照接线文档)
typedef struct {
    GPIO_Regs* port;
    uint32_t pin;
} sensor_pin_t;

static const sensor_pin_t sensor_pins[SENSOR_COUNT] = {
    {GPIO_GRP_0_HDPIN_0_PORT, GPIO_GRP_0_HDPIN_0_PIN},  // OUT1 -> PA9
    {GPIO_GRP_0_HDPIN_6_PORT, GPIO_GRP_0_HDPIN_6_PIN},  // OUT2 -> PB7
    {GPIO_GRP_0_HDPIN_1_PORT, GPIO_GRP_0_HDPIN_1_PIN},  // OUT3 -> PA27
    {GP<PERSON>_GRP_0_HDPIN_2_PORT, GPIO_GRP_0_HDPIN_2_PIN},  // OUT4 -> PA24
    {GPIO_GRP_0_HDPIN_3_PORT, GPIO_GRP_0_HDPIN_3_PIN},  // OUT5 -> PB16
    {GPIO_GRP_0_HDPIN_4_PORT, GPIO_GRP_0_HDPIN_4_PIN},  // OUT6 -> PA12
    {GPIO_GRP_0_HDPIN_5_PORT, GPIO_GRP_0_HDPIN_5_PIN}   // OUT7 -> PB6
};

// 速度参数定义 (根据老板转弯思路优化，慢慢动态调整)
#define NORMAL_SPEED    1500    // 正常直行速度
#define HIGH_SPEED      2000    // 高速(转向时快轮) - 最高不超过2000
#define LOW_SPEED       800     // 低速(转向时慢轮)
#define TURN_SPEED      1200    // 转弯时的基础速度
#define GENTLE_TURN     1000    // 温和转弯速度 (转弯处慢慢调整)
#define STOP_SPEED      0       // 停止

// 传感器状态数组
static int sensor_values[SENSOR_COUNT];

// 【全局调试变量】供Keil5调试器观察
volatile uint32_t debug_gpio_raw[7] = {0};     // 原始GPIO状态
volatile int debug_result[7] = {0};            // 处理后结果
volatile int global_sensor_count = 0;          // 传感器计数
volatile int emergency_debug[12] = {0};        // 紧急调试数组

// 调试计数器和状态变量
static int debug_counter = 0;
static int lost_line_counter = 0;              // 丢线计数器
static float last_position = 0.0f;             // 上次位置记录

// 循迹状态枚举
typedef enum {
    LINE_FOLLOW_NORMAL,     // 正常循迹
    LINE_FOLLOW_LOST,       // 丢线状态
    LINE_FOLLOW_TURNING,    // 转弯状态
    LINE_FOLLOW_RECOVERING  // 恢复状态
} line_follow_state_t;

static line_follow_state_t current_state = LINE_FOLLOW_NORMAL;

// 传感器权重 (从右到左: OUT1,OUT2,OUT3,OUT4,OUT5,OUT6,OUT8)
static const float sensor_weights[SENSOR_COUNT] = {-3.0f, -2.0f, -1.0f, 0.0f, 1.0f, 2.0f, 3.0f};

/**
 * @brief 读取单个灰度传感器状态
 * @param sensor_index 传感器索引 (0-5)
 * @return 传感器状态 (0=白色, 1=黑色)
 */
static int read_sensor(int sensor_index)
{
    if (sensor_index >= SENSOR_COUNT) return 0;

    // 读取GPIO原始状态
    uint32_t gpio_raw = DL_GPIO_readPins(sensor_pins[sensor_index].port, sensor_pins[sensor_index].pin);

    // 灰度传感器：检测到黑线时输出低电平(LED熄灭)，检测到白色时输出高电平(LED亮)
    // 所以我们需要反转逻辑：GPIO读到0(低电平)表示黑线，返回1；GPIO读到1(高电平)表示白色，返回0
    int result = gpio_raw ? 0 : 1;

    // 【紧急调试】记录原始GPIO状态到全局变量，方便Keil5调试器观察
    debug_gpio_raw[sensor_index] = gpio_raw;
    debug_result[sensor_index] = result;

    return result;
}

/**
 * @brief 读取所有灰度传感器状态
 */
static void read_all_sensors(void)
{
    for (int i = 0; i < SENSOR_COUNT; i++) {
        sensor_values[i] = read_sensor(i);
    }
}

/**
 * @brief 计算线的位置 (基于加权平均)
 * @return 线的位置 (-3.0到3.0，0为中央，负值表示偏左，正值表示偏右)
 */
static float calculate_line_position(void)
{
    float weighted_sum = 0.0f;
    int sensor_sum = 0;

    // 计算加权和
    for (int i = 0; i < SENSOR_COUNT; i++) {
        if (sensor_values[i] == 1) {
            weighted_sum += sensor_weights[i];
            sensor_sum += 1;
        }
    }

    // 如果没有传感器检测到线，返回上次位置
    if (sensor_sum == 0) {
        return last_position;
    }

    // 计算加权平均位置
    float position = weighted_sum / sensor_sum;
    last_position = position;

    return position;
}

/**
 * @brief 获取传感器检测到的线的数量
 * @return 检测到黑线的传感器数量
 */
static int get_line_sensor_count(void)
{
    int count = 0;
    for (int i = 0; i < SENSOR_COUNT; i++) {
        if (sensor_values[i] == 1) {
            count++;
        }
    }
    return count;
}

/**
 * @brief 设置电机速度和方向 (完整的电机驱动)
 * @param left_speed 左电机速度 (0-8000)
 * @param right_speed 右电机速度 (0-8000)
 */
static void set_motor_speed(int left_speed, int right_speed)
{
    // 限制PWM值范围
    if (left_speed > 8000) left_speed = 8000;
    if (left_speed < 0) left_speed = 0;
    if (right_speed > 8000) right_speed = 8000;
    if (right_speed < 0) right_speed = 0;

    // 确保电机方向设置为前进
    DL_GPIO_setPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN1_PIN);    // A电机正转
    DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN2_PIN);
    DL_GPIO_setPins(MOTOR_DIR_PORT, MOTOR_DIR_BIN1_PIN);    // B电机正转
    DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_BIN2_PIN);

    // 设置PWM值 (已修正硬件对应关系)
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, right_speed, GPIO_PWM_0_C0_IDX);  // 右电机
    DL_TimerA_setCaptureCompareValue(PWM_0_INST, left_speed, GPIO_PWM_0_C1_IDX);   // 左电机

    // 确保PWM输出启用
    DL_TimerA_setCaptureCompareOutCtl(PWM_0_INST, DL_TIMER_CC_OCTL_INIT_VAL_LOW,
                                      DL_TIMER_CC_OCTL_INV_OUT_DISABLED,
                                      DL_TIMER_CC_OCTL_SRC_FUNCVAL,
                                      DL_TIMERA_CAPTURE_COMPARE_0_INDEX);
    DL_TimerA_setCaptureCompareOutCtl(PWM_0_INST, DL_TIMER_CC_OCTL_INIT_VAL_LOW,
                                      DL_TIMER_CC_OCTL_INV_OUT_DISABLED,
                                      DL_TIMER_CC_OCTL_SRC_FUNCVAL,
                                      DL_TIMERA_CAPTURE_COMPARE_1_INDEX);
}

/**
 * @brief 灰度传感器循迹控制主函数 (重新设计)
 * @note 基于位置计算的智能循迹算法，支持多场景
 */
void line_follow_control(void)
{
    // 读取所有传感器状态
    read_all_sensors();

    // 计算线的位置和传感器数量
    float line_position = calculate_line_position();
    int sensor_count = get_line_sensor_count();

    // 更新全局调试变量
    global_sensor_count = sensor_count;

    // 详细调试输出结构 (全局可访问)
    volatile struct {
        int sensors[7];           // 传感器状态: [OUT1,OUT2,OUT3,OUT4,OUT5,OUT6,OUT8]
        float position;           // 计算的位置 (-3.0到3.0)
        int count;               // 检测到线的传感器数量
        int state;               // 当前状态
        int lost_counter;        // 丢线计数
        int left_speed;          // 左电机速度
        int right_speed;         // 右电机速度
    } debug_info;

    // 更新调试信息
    for(int i = 0; i < 7; i++) {
        debug_info.sensors[i] = sensor_values[i];
    }
    debug_info.position = line_position;
    debug_info.count = sensor_count;
    debug_info.state = current_state;
    debug_info.lost_counter = lost_line_counter;

    // LED调试指示 (每50次循环，约0.25秒，更频繁)
    debug_counter++;
    if (debug_counter >= 50) {
        DL_GPIO_togglePins(LED_PORT, LED_UserLED_PIN);
        debug_counter = 0;

        // 紧急调试：更新全局调试变量，方便Keil5调试器观察 (按老板接线)
        emergency_debug[0] = sensor_values[0];  // OUT1状态 (PA9)
        emergency_debug[1] = sensor_values[1];  // OUT2状态 (PB7)
        emergency_debug[2] = sensor_values[2];  // OUT3状态 (PA27)
        emergency_debug[3] = sensor_values[3];  // OUT4状态 (PA24)
        emergency_debug[4] = sensor_values[4];  // OUT5状态 (PB16)
        emergency_debug[5] = sensor_values[5];  // OUT6状态 (PA12)
        emergency_debug[6] = sensor_values[6];  // OUT7状态 (PB6)
        emergency_debug[7] = sensor_count;      // 检测到黑线的传感器总数
        emergency_debug[8] = debug_info.left_speed;   // 左电机速度
        emergency_debug[9] = debug_info.right_speed;  // 右电机速度
        emergency_debug[10] = (int)(line_position * 100); // 位置*100
        emergency_debug[11] = 0xABCD;          // 标识符
    }

    // 智能循迹逻辑 - 基于位置计算和状态机
    // 传感器排列: [0][1][2][3][4][5] 对应 OUT1,OUT3,OUT4,OUT5,OUT6,OUT8
    // 权重: [-3,-2,-1,1,2,3] (从右到左，负值表示偏左，正值表示偏右)

    int left_speed = NORMAL_SPEED;
    int right_speed = NORMAL_SPEED;

    // 场景1: 完全丢线 (所有传感器都为0)
    if (sensor_count == 0) {
        lost_line_counter++;
        current_state = LINE_FOLLOW_LOST;

        if (lost_line_counter < 20) {
            // 短时间丢线，保持上次方向继续前进
            left_speed = TURN_SPEED;
            right_speed = TURN_SPEED;
        } else {
            // 长时间丢线，逆时针搜索
            left_speed = HIGH_SPEED;
            right_speed = STOP_SPEED;
        }
        debug_info.left_speed = left_speed;
        debug_info.right_speed = right_speed;
        set_motor_speed(left_speed, right_speed);
        return;
    }

    // 重置丢线计数器
    lost_line_counter = 0;
    current_state = LINE_FOLLOW_NORMAL;

    // 【紧急测试模式】先验证传感器是否真的在工作
    // 传感器排列: [0][1][2][3][4][5][6] 对应 OUT1,OUT2,OUT3,OUT4,OUT5,OUT6,OUT7 (从右到左)
    // 老板接线: OUT1-PA9, OUT2-PB7, OUT3-PA27, OUT4-PA24, OUT5-PB16, OUT6-PA12, OUT7-PB6
    // HDPIN映射: HDPIN_0,HDPIN_6,HDPIN_1,HDPIN_2,HDPIN_3,HDPIN_4,HDPIN_5 (按老板接线)

    current_state = LINE_FOLLOW_NORMAL;

    // 【测试模式】：如果任何传感器检测到黑线，就停止电机，方便观察
    if (sensor_count > 0) {
        // 有传感器检测到黑线，停止电机，LED常亮
        left_speed = 0;
        right_speed = 0;
        DL_GPIO_setPins(LED_PORT, LED_UserLED_PIN);  // LED常亮表示检测到黑线
    } else {
        // 没有传感器检测到黑线，慢速前进，LED闪烁
        left_speed = 800;
        right_speed = 800;
        // LED闪烁在下面的调试代码中处理
    }


    // 记录调试信息并执行电机控制
    debug_info.left_speed = left_speed;
    debug_info.right_speed = right_speed;
    set_motor_speed(left_speed, right_speed);
}

/**
 * @brief 获取传感器状态用于调试
 * @param index 传感器索引 (0-5)
 * @return 传感器状态 (0=白色, 1=黑色)
 */
int get_sensor_value(int index)
{
    if (index >= SENSOR_COUNT) return 0;
    return sensor_values[index];
}

/**
 * @brief 灰度传感器初始化
 * @note GPIO已在syscfg中配置，此函数确保电机系统正常启动
 */
void line_follow_init(void)
{
    // GPIO配置已在ti_msp_dl_config.c中完成

    // 初始化传感器状态
    for (int i = 0; i < SENSOR_COUNT; i++) {
        sensor_values[i] = 0;
    }

    // 确保PWM定时器启动
    DL_TimerA_startCounter(PWM_0_INST);

    // 初始化电机为停止状态
    set_motor_speed(0, 0);

    // 短暂延时让系统稳定
    for(volatile int i = 0; i < 100000; i++);

    // 启动时给一个较低的前进速度，确保电机工作且速度适中
    set_motor_speed(NORMAL_SPEED, NORMAL_SPEED);
}

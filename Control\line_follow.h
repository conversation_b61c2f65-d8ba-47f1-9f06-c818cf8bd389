/**
 * @file line_follow.h
 * @brief 灰度传感器循迹控制模块头文件
 * <AUTHOR> (Engineer)
 * @date 2025-01-31
 */

#ifndef LINE_FOLLOW_H
#define LINE_FOLLOW_H

#include <stdint.h>

/**
 * @brief 灰度传感器循迹控制主函数
 * @note 基于暴力解法的循迹算法，需要在主循环中定期调用
 */
void line_follow_control(void);

/**
 * @brief 灰度传感器初始化
 * @note GPIO已在syscfg中配置，此函数预留用于后续扩展
 */
void line_follow_init(void);

/**
 * @brief 获取传感器状态用于调试
 * @param index 传感器索引 (0-5)，对应OUT1,OUT3,OUT4,OUT5,OUT6,OUT8
 * @return 传感器状态 (0=白色, 1=黑色)
 */
int get_sensor_value(int index);

#endif // LINE_FOLLOW_H

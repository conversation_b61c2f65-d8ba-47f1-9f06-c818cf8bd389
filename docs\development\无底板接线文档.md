# 无底板接线文档

## 电池
正负极分别连接电源模块1和2的正负极

## 电源模块1
(左边是电源模块上的,右边M0板子上的，--表示左边连右边）
5V*2—5V*2 
GND*2—GND*2
这部分连完之后，板子上的5V等于电源模块的5V、GND也等于GND，所以下面的5V和GND连主控板上和连电源模块上都一样，都一样！如果主控板子上没5V，就连电源模块的5V

## 电源模块2
12V/24V输出—TB6612的VM
5V输出—K210的5V
GND—K210的GND、串口舵机的负极

## TB6612电机驱动器        有关电源部分都接电源模块2
（左边是TB6612上的,右边是电源模块和电机上的，--表示左边连右边）
VM—电源模块2的12V/24V
VCC—电源模块1的5V
3*GND—电源模块1的GND
AO1—右轮电机正极（在编码器板子引出的引脚上）
AO2—右轮电机负极（在编码器板子引出的引脚上）
BO1—左轮电机正极（在编码器板子引出的引脚上）
BO2—左轮电机负极（在编码器板子引出的引脚上）	

（左边是TB6612上的,右边是M0板子上的引脚）
PWMA—PB2
AIN2—PA17
AIN1—PA16
STBY—电源模块1的5V
BIN1—PA14
BIN2—PA13
PWMB—PB3

## 电机编码器         
（左边是电机编码器板子上的,右边是M0板子上的引脚）
右轮编码器B相—PA25
右轮编码器A相—PA26
左轮编码器B相—PB20
左轮编码器A相—PB24

## 模拟按键
第一个PA8接GND表示按下
第二个是PB7,接3.3V表示按下

## 灰度传感器(只接6路)  有关电源部分都接电源模块1
（左边是灰度板子上的，右边是M0板子上的）
OUT1—PA9
OUT3—PA27
OUT4—PA24
OUT5—PB16
OUT6—PA12
OUT8—PB6
VCC—电源模块1的5V
GND—电源模块1的GND

## 串口舵机（只需要连最下面的舵机）有关电源部分都接电源模块2
S—K210的7脚
正极—电源模块2的7.4V
负极—电源模块2的GND

## K210    有关电源部分都接电源模块2
（左边是K210上的引脚,右边是电源模块和M0底板上的，--表示左边连右边）
24—PB17
GND—电源模块2的GND
5V—电源模块2上的5V
7—串口舵机的S

## MPU6050陀螺仪      有关电源部分都接电源模块1（注释：未使用）
（左边是MPU6050上的,右边是M0板子上的引脚）
VCC—电源模块1的5V
GND—电源模块1的GND
SCL—PA1
SDA—PA0
INT—PA7

## 屏幕         有关电源部分都接电源模块1（注释：未使用）
（左边是屏幕上的,右边是M0板子上的引脚）
VCC—电源模块1的3.3V
GND—电源模块1的GND
SCL—PA28
SDA—PA31

## 万向轮
前万向轮—仅机械安装，无电线连接

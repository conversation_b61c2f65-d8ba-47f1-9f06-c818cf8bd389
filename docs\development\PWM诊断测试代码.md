# PWM通道诊断测试代码

## 问题分析

根据代码分析，PWM配置看起来是正确的：
- 两个通道都正确初始化
- GPIO引脚都正确配置为PWM功能
- 定时器时钟和周期配置正常

但左电机(C1通道)不响应PWM值变化，可能的原因：

### 1. 软件层面可能问题
- PWM通道输出方向配置
- 捕获比较寄存器更新方法
- 定时器启动状态

### 2. 建议的诊断测试代码

```c
// 在main函数中添加详细的PWM诊断测试
int main(void)
{
    SYSCFG_DL_init();
    
    // 启动PWM定时器
    DL_TimerA_startCounter(PWM_0_INST);
    
    // 设置电机方向
    DL_GPIO_setPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN1_PIN | MOTOR_DIR_BIN1_PIN);
    DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN2_PIN | MOTOR_DIR_BIN2_PIN);
    
    // PWM诊断测试序列
    int test_phase = 0;
    int delay_counter = 0;
    
    while (1) {
        // LED指示当前测试阶段
        if (delay_counter % 500000 == 0) {
            DL_GPIO_togglePins(LED_PORT, LED_UserLED_PIN);
        }
        
        switch(test_phase) {
            case 0: // 测试阶段0：只有C0通道输出
                DL_TimerA_setCaptureCompareValue(PWM_0_INST, 3000, GPIO_PWM_0_C0_IDX);
                DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, GPIO_PWM_0_C1_IDX);
                break;
                
            case 1: // 测试阶段1：只有C1通道输出
                DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, GPIO_PWM_0_C0_IDX);
                DL_TimerA_setCaptureCompareValue(PWM_0_INST, 3000, GPIO_PWM_0_C1_IDX);
                break;
                
            case 2: // 测试阶段2：两个通道都输出
                DL_TimerA_setCaptureCompareValue(PWM_0_INST, 2000, GPIO_PWM_0_C0_IDX);
                DL_TimerA_setCaptureCompareValue(PWM_0_INST, 4000, GPIO_PWM_0_C1_IDX);
                break;
                
            case 3: // 测试阶段3：交换PWM值
                DL_TimerA_setCaptureCompareValue(PWM_0_INST, 4000, GPIO_PWM_0_C0_IDX);
                DL_TimerA_setCaptureCompareValue(PWM_0_INST, 2000, GPIO_PWM_0_C1_IDX);
                break;
        }
        
        delay_counter++;
        
        // 每2秒切换一个测试阶段
        if (delay_counter >= 2000000) {
            delay_counter = 0;
            test_phase = (test_phase + 1) % 4;
        }
    }
}
```

### 3. 可能的修复方案

#### 方案A：强制重新配置C1通道
```c
// 在PWM设置前添加
DL_TimerA_setCaptureCompareOutCtl(PWM_0_INST, 
    DL_TIMER_CC_OCTL_INIT_VAL_LOW,
    DL_TIMER_CC_OCTL_INV_OUT_DISABLED, 
    DL_TIMER_CC_OCTL_SRC_FUNCVAL,
    DL_TIMERA_CAPTURE_COMPARE_1_INDEX);
```

#### 方案B：检查并重新启用C1输出
```c
// 确保C1通道输出使能
DL_TimerA_setCCPDirection(PWM_0_INST, DL_TIMER_CC0_OUTPUT | DL_TIMER_CC1_OUTPUT);
```

#### 方案C：使用立即更新模式
```c
// 强制使用立即更新模式
DL_TimerA_setCaptCompUpdateMethod(PWM_0_INST, 
    DL_TIMER_CC_UPDATE_METHOD_IMMEDIATE, 
    DL_TIMERA_CAPTURE_COMPARE_1_INDEX);
```

### 4. 硬件检查建议

1. **示波器测试**：
   - 测量PB2 (C0) 和 PB3 (C1) 的PWM波形
   - 确认C1通道是否有PWM输出

2. **万用表测试**：
   - 测量PB3引脚的电压变化
   - 确认引脚是否被正确配置

3. **交换测试**：
   - 临时交换C0和C1的PWM值设置
   - 观察电机响应是否跟随PWM设置

### 5. 调试输出建议

```c
// 添加调试信息输出（如果有UART）
printf("PWM Period: %d\n", DL_TimerA_getLoadValue(PWM_0_INST));
printf("C0 Compare: %d\n", DL_TimerA_getCaptureCompareValue(PWM_0_INST, DL_TIMER_CC_0_INDEX));
printf("C1 Compare: %d\n", DL_TimerA_getCaptureCompareValue(PWM_0_INST, DL_TIMER_CC_1_INDEX));
```

## 结论

从软件配置来看，PWM设置应该是正确的。建议：
1. 先运行诊断测试代码确认问题
2. 使用示波器检查PB3引脚的PWM输出
3. 如果软件配置无误，可能是硬件连接问题

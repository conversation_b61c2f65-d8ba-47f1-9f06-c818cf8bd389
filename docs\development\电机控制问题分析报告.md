# 电机控制问题分析报告

## 问题描述
用户反馈：上电测试时右轮电机向前转动，左轮电机不转。

## 代码分析结果

### 1. PWM配置分析

**PWM硬件配置：**
- PWM定时器：TIMA1 (PWM_0_INST)
- PWM频率：80MHz / 8000 = 10kHz
- PWM通道配置：
  - GPIO_PWM_0_C0 (PB2) - 对应左电机PWM
  - GPIO_PWM_0_C1 (PB3) - 对应右电机PWM

**PWM引脚初始化：**
```c
// 在ti_msp_dl_config.c中正确配置了PWM引脚
DL_GPIO_initPeripheralOutputFunction(GPIO_PWM_0_C0_IOMUX,GPIO_PWM_0_C0_IOMUX_FUNC);
DL_GPIO_enableOutput(GPIO_PWM_0_C0_PORT, GPIO_PWM_0_C0_PIN);
DL_GPIO_initPeripheralOutputFunction(GPIO_PWM_0_C1_IOMUX,GPIO_PWM_0_C1_IOMUX_FUNC);
DL_GPIO_enableOutput(GPIO_PWM_0_C1_PORT, GPIO_PWM_0_C1_PIN);
```

### 2. 电机方向控制分析

**方向控制引脚配置：**
- 左电机：AIN1 (PA16), AIN2 (PA17)
- 右电机：BIN1 (PA14), BIN2 (PA13)

**当前方向设置（main函数）：**
```c
// 设置电机方向为正转
DL_GPIO_setPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN1_PIN | MOTOR_DIR_BIN1_PIN);
DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN2_PIN | MOTOR_DIR_BIN2_PIN);
```

### 3. PWM输出设置分析

**主程序中的PWM设置：**
```c
// 固定PWM输出，方向不变
DL_TimerA_setCaptureCompareValue(PWM_0_INST, 4000, GPIO_PWM_0_C0_IDX); // 左电机
DL_TimerA_setCaptureCompareValue(PWM_0_INST, 4000, GPIO_PWM_0_C1_IDX); // 右电机
```

**balance.c中的PWM设置：**
```c
// 调试：强制设置PWM值
DL_TimerA_setCaptureCompareValue(PWM_0_INST, 4000, GPIO_PWM_0_C0_IDX);  // 右电机
DL_TimerA_setCaptureCompareValue(PWM_0_INST, 4000, GPIO_PWM_0_C1_IDX);  // 左电机
```

## 问题发现

### 关键问题：PWM通道分配混乱

**在代码中发现了PWM通道分配的不一致性：**

1. **main函数中的注释：**
   - GPIO_PWM_0_C0_IDX 标注为"左电机"
   - GPIO_PWM_0_C1_IDX 标注为"右电机"

2. **balance.c中的注释：**
   - GPIO_PWM_0_C0_IDX 标注为"右电机"
   - GPIO_PWM_0_C1_IDX 标注为"左电机"

3. **硬件引脚分配：**
   - GPIO_PWM_0_C0 = PB2 (TIMA1_CCP0)
   - GPIO_PWM_0_C1 = PB3 (TIMA1_CCP1)

### 初始化问题

**GPIO初始化中的问题：**
```c
// 在ti_msp_dl_config.c中，所有电机方向引脚都被初始化为高电平
DL_GPIO_setPins(GPIOA, MOTOR_DIR_AIN1_PIN |
        MOTOR_DIR_AIN2_PIN |
        MOTOR_DIR_BIN1_PIN |
        MOTOR_DIR_BIN2_PIN);
```

这意味着在系统初始化时，所有方向控制引脚都是高电平，这可能导致电机驱动器进入制动状态。

## 问题原因分析

### 1. 左电机不转的可能原因：

1. **PWM通道分配混乱**：代码中对PWM通道的标注不一致，可能导致实际控制的电机与预期不符。

2. **方向控制引脚冲突**：初始化时所有方向引脚都设为高电平，可能导致电机驱动器无法正常工作。

3. **硬件连接问题**：需要确认实际硬件连接是否与代码中的引脚定义一致。

### 2. 右电机能转的原因：

可能是因为硬件连接或驱动器特性使得某个通道在当前配置下能够正常工作。

## 建议解决方案

### 1. 立即验证方案
```c
// 在main函数开始处添加明确的测试代码
// 测试左电机单独运行
DL_GPIO_setPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN1_PIN);
DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_AIN2_PIN);
DL_GPIO_clearPins(MOTOR_DIR_PORT, MOTOR_DIR_BIN1_PIN | MOTOR_DIR_BIN2_PIN);
DL_TimerA_setCaptureCompareValue(PWM_0_INST, 4000, GPIO_PWM_0_C0_IDX);
DL_TimerA_setCaptureCompareValue(PWM_0_INST, 0, GPIO_PWM_0_C1_IDX);
```

### 2. 系统性解决方案
1. 统一PWM通道标注
2. 修正GPIO初始化逻辑
3. 添加电机测试函数
4. 验证硬件连接

## 结论

**当前现象（右轮转，左轮不转）不正常**。问题主要源于：
1. PWM通道分配标注混乱
2. GPIO初始化配置不当
3. 缺乏系统性的电机测试机制

建议立即进行单电机测试来确定正确的PWM通道分配，然后修正代码中的标注和初始化逻辑。
